package com.gulderbone.recyclerviewrevolut

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlin.random.Random

class CurrencyService {

    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private var updateJob: Job? = null

    // Fixed list of 5 currencies with their base rates
    private val baseCurrencies = listOf(
        CurrencyValue("USD", 1.0),
        CurrencyValue("EUR", 0.85),
        <PERSON><PERSON><PERSON>cyValue("GBP", 0.73),
        <PERSON><PERSON><PERSON>cyValue("JPY", 110.0),
        CurrencyValue("CHF", 0.92)
    )

    private val _currencyUpdates = MutableSharedFlow<List<CurrencyValue>>(replay = 1)
    val currencyUpdates: SharedFlow<List<CurrencyValue>> = _currencyUpdates.asSharedFlow()

    private var isRunning = false

    /**
     * Starts the currency service to emit updated currency values every second
     */
    fun start() {
        if (isRunning) return

        isRunning = true
        updateJob = scope.launch {
            while (isRunning) {
                val updatedCurrencies = baseCurrencies.map { currency ->
                    CurrencyValue(
                        currencyCode = currency.currencyCode,
                        convertedAmount = generateRandomAmount(currency.convertedAmount)
                    )
                }

                _currencyUpdates.emit(updatedCurrencies)
                delay(1000) // Update every second
            }
        }
    }

    /**
     * Stops the currency service
     */
    fun stop() {
        isRunning = false
        updateJob?.cancel()
        updateJob = null
    }

    /**
     * Generates a random amount based on the base rate with realistic fluctuations
     */
    private fun generateRandomAmount(baseRate: Double): Double {
        // Generate fluctuation between -5% to +5% of the base rate
        val fluctuationPercent = Random.nextDouble(-0.05, 0.05)
        val fluctuation = baseRate * fluctuationPercent
        return (baseRate + fluctuation).coerceAtLeast(0.01) // Ensure positive values
    }

    /**
     * Gets the current currency values (without starting the service)
     */
    fun getCurrentCurrencies(): List<CurrencyValue> {
        return baseCurrencies.map { currency ->
            CurrencyValue(
                currencyCode = currency.currencyCode,
                convertedAmount = generateRandomAmount(currency.convertedAmount)
            )
        }
    }

    /**
     * Cleanup resources
     */
    fun cleanup() {
        stop()
        scope.cancel()
    }
}

data class CurrencyValue(
    var currencyCode: String,
    var convertedAmount: Double
)