package com.gulderbone.recyclerviewrevolut

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class MainActivity : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: ItemAdapter

    private val items = mutableListOf<Item>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        recyclerView = findViewById(R.id.recycler_view)

        adapter = ItemAdapter(items)

        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter

        recyclerView.addItemDecoration(SimpleDividerDecoration(recyclerView, 10, android.R.color.holo_red_dark))

        loadSampleData()
    }

    private fun loadSampleData() {
        val sampleItems = listOf(
            Item(1, "Item 1", 1.00),
            Item(2, "Item 2", 2.00),
            Item(3, "Item 3", 3.00),
            Item(4, "Item 4", 4.00),
            Item(5, "Item 5", 5.00),
        )

        items.addAll(sampleItems)
        adapter.notifyDataSetChanged()
    }
}